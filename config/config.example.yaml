# AK/SK 管理系统配置文件示例

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

# 数据库配置
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_password"
  database: "aksk"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"

# 安全配置
security:
  # 32字节的加密密钥，用于AK/SK加密存储
  encryption_key: "your-32-byte-encryption-key-here!!"
  # JWT密钥
  jwt_secret: "your-jwt-secret-key-here"
  # Token过期时间
  token_expiry: "24h"
  # 限流配置
  rate_limit:
    enabled: true
    rps: 100      # 每秒请求数
    burst: 200    # 突发请求数

# 日志配置
log:
  level: "info"           # debug, info, warn, error
  format: "json"          # json, text
  output: "stdout"        # stdout, stderr, file path
  max_size: 100          # MB
  max_backups: 3
  max_age: 28            # days
  compress: true
