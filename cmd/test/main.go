package main

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	// 创建简单的测试服务器
	router := gin.Default()

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"status": "healthy",
			"message": "AK/SK Management System is running",
		})
	})

	// 测试端点
	router.GET("/test", func(c *gin.Context) {
		c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
			"message": "Test endpoint working",
		})
	})

	fmt.Println("Starting test server on :8080")
	fmt.Println("Health check: http://localhost:8080/health")
	fmt.Println("Test endpoint: http://localhost:8080/test")

	// 启动服务器
	if err := router.Run(":8080"); err != nil {
		fmt.Printf("Failed to start server: %v\n", err)
	}
}
