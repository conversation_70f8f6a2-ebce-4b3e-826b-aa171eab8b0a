package repository

import (
	"aksk/internal/models"
	"time"
	"gorm.io/gorm"
)

// TokenRepository Token数据访问层
type TokenRepository struct {
	db *gorm.DB
}

// NewTokenRepository 创建Token仓库
func NewTokenRepository(db *gorm.DB) *TokenRepository {
	return &TokenRepository{db: db}
}

// Create 创建Token
func (r *TokenRepository) Create(token *models.Token) error {
	return r.db.Create(token).Error
}

// GetByID 根据ID获取Token
func (r *TokenRepository) GetByID(id uint) (*models.Token, error) {
	var token models.Token
	err := r.db.First(&token, id).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByTokenHash 根据Token哈希获取Token
func (r *TokenRepository) GetByTokenHash(tokenHash string) (*models.Token, error) {
	var token models.Token
	err := r.db.Where("token_hash = ?", tokenHash).First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// List 获取Token列表
func (r *TokenRepository) List(offset, limit int, status *int8) ([]*models.Token, int64, error) {
	var tokens []*models.Token
	var total int64

	query := r.db.Model(&models.Token{})
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&tokens).Error
	if err != nil {
		return nil, 0, err
	}

	return tokens, total, nil
}

// Update 更新Token
func (r *TokenRepository) Update(token *models.Token) error {
	return r.db.Save(token).Error
}

// Delete 删除Token
func (r *TokenRepository) Delete(id uint) error {
	return r.db.Delete(&models.Token{}, id).Error
}

// Exists 检查Token是否存在
func (r *TokenRepository) Exists(id uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.Token{}).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// ExistsByName 检查Token名称是否存在
func (r *TokenRepository) ExistsByName(name string, excludeID ...uint) (bool, error) {
	query := r.db.Model(&models.Token{}).Where("token_name = ?", name)
	
	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	
	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// UpdateStatus 更新Token状态
func (r *TokenRepository) UpdateStatus(id uint, status int8) error {
	return r.db.Model(&models.Token{}).Where("id = ?", id).Update("status", status).Error
}

// GetExpiredTokens 获取过期的Token
func (r *TokenRepository) GetExpiredTokens() ([]*models.Token, error) {
	var tokens []*models.Token
	now := time.Now()
	
	err := r.db.Where("expires_at IS NOT NULL AND expires_at <= ? AND status = 1", 
		now).Find(&tokens).Error
	if err != nil {
		return nil, err
	}
	
	return tokens, nil
}

// UpdateLastUsedAt 更新最后使用时间
func (r *TokenRepository) UpdateLastUsedAt(id uint, lastUsedAt time.Time) error {
	return r.db.Model(&models.Token{}).Where("id = ?", id).Update("last_used_at", lastUsedAt).Error
}

// GetActiveTokens 获取活跃的Token
func (r *TokenRepository) GetActiveTokens() ([]*models.Token, error) {
	var tokens []*models.Token
	now := time.Now()
	
	err := r.db.Where("status = 1 AND (expires_at IS NULL OR expires_at > ?)", 
		now).Find(&tokens).Error
	if err != nil {
		return nil, err
	}
	
	return tokens, nil
}
