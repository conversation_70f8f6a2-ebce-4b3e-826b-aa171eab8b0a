package repository

import (
	"aksk/internal/models"
	"gorm.io/gorm"
)

// BusinessRepository 业务数据访问层
type BusinessRepository struct {
	db *gorm.DB
}

// NewBusinessRepository 创建业务仓库
func NewBusinessRepository(db *gorm.DB) *BusinessRepository {
	return &BusinessRepository{db: db}
}

// Create 创建业务
func (r *BusinessRepository) Create(business *models.Business) error {
	return r.db.Create(business).Error
}

// GetByID 根据ID获取业务
func (r *BusinessRepository) GetByID(id uint) (*models.Business, error) {
	var business models.Business
	err := r.db.First(&business, id).Error
	if err != nil {
		return nil, err
	}
	return &business, nil
}

// GetByName 根据名称获取业务
func (r *BusinessRepository) GetByName(name string) (*models.Business, error) {
	var business models.Business
	err := r.db.Where("name = ?", name).First(&business).Error
	if err != nil {
		return nil, err
	}
	return &business, nil
}

// List 获取业务列表
func (r *BusinessRepository) List(offset, limit int, status *int8) ([]*models.Business, int64, error) {
	var businesses []*models.Business
	var total int64

	query := r.db.Model(&models.Business{})
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&businesses).Error
	if err != nil {
		return nil, 0, err
	}

	return businesses, total, nil
}

// Update 更新业务
func (r *BusinessRepository) Update(business *models.Business) error {
	return r.db.Save(business).Error
}

// Delete 删除业务
func (r *BusinessRepository) Delete(id uint) error {
	return r.db.Delete(&models.Business{}, id).Error
}

// Exists 检查业务是否存在
func (r *BusinessRepository) Exists(id uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.Business{}).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// ExistsByName 检查业务名称是否存在
func (r *BusinessRepository) ExistsByName(name string, excludeID ...uint) (bool, error) {
	query := r.db.Model(&models.Business{}).Where("name = ?", name)
	
	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	
	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// GetWithAccessKeys 获取业务及其关联的访问密钥
func (r *BusinessRepository) GetWithAccessKeys(id uint) (*models.Business, error) {
	var business models.Business
	err := r.db.Preload("AccessKeys").First(&business, id).Error
	if err != nil {
		return nil, err
	}
	return &business, nil
}

// UpdateStatus 更新业务状态
func (r *BusinessRepository) UpdateStatus(id uint, status int8) error {
	return r.db.Model(&models.Business{}).Where("id = ?", id).Update("status", status).Error
}
