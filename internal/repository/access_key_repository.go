package repository

import (
	"aksk/internal/models"
	"time"
	"gorm.io/gorm"
)

// AccessKeyRepository AK/SK数据访问层
type AccessKeyRepository struct {
	db *gorm.DB
}

// NewAccessKeyRepository 创建AK/SK仓库
func NewAccessKeyRepository(db *gorm.DB) *AccessKeyRepository {
	return &AccessKeyRepository{db: db}
}

// Create 创建AK/SK
func (r *AccessKeyRepository) Create(accessKey *models.AccessKey) error {
	return r.db.Create(accessKey).Error
}

// GetByID 根据ID获取AK/SK
func (r *AccessKeyRepository) GetByID(id uint) (*models.AccessKey, error) {
	var accessKey models.AccessKey
	err := r.db.Preload("Business").First(&accessKey, id).Error
	if err != nil {
		return nil, err
	}
	return &accessKey, nil
}

// GetByAccessKeyID 根据AccessKeyID获取AK/SK
func (r *AccessKeyRepository) GetByAccessKeyID(accessKeyID string) (*models.AccessKey, error) {
	var accessKey models.AccessKey
	err := r.db.Preload("Business").Where("access_key_id = ?", accessKeyID).First(&accessKey).Error
	if err != nil {
		return nil, err
	}
	return &accessKey, nil
}

// List 获取AK/SK列表
func (r *AccessKeyRepository) List(offset, limit int, businessID *uint, status *int8) ([]*models.AccessKey, int64, error) {
	var accessKeys []*models.AccessKey
	var total int64

	query := r.db.Model(&models.AccessKey{}).Preload("Business")
	
	if businessID != nil {
		query = query.Where("business_id = ?", *businessID)
	}
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&accessKeys).Error
	if err != nil {
		return nil, 0, err
	}

	return accessKeys, total, nil
}

// Update 更新AK/SK
func (r *AccessKeyRepository) Update(accessKey *models.AccessKey) error {
	return r.db.Save(accessKey).Error
}

// Delete 删除AK/SK
func (r *AccessKeyRepository) Delete(id uint) error {
	return r.db.Delete(&models.AccessKey{}, id).Error
}

// Exists 检查AK/SK是否存在
func (r *AccessKeyRepository) Exists(id uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.AccessKey{}).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// ExistsByAccessKeyID 检查AccessKeyID是否存在
func (r *AccessKeyRepository) ExistsByAccessKeyID(accessKeyID string, excludeID ...uint) (bool, error) {
	query := r.db.Model(&models.AccessKey{}).Where("access_key_id = ?", accessKeyID)
	
	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	
	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// UpdateStatus 更新AK/SK状态
func (r *AccessKeyRepository) UpdateStatus(id uint, status int8) error {
	return r.db.Model(&models.AccessKey{}).Where("id = ?", id).Update("status", status).Error
}

// GetActiveByBusinessID 获取业务的活跃AK/SK
func (r *AccessKeyRepository) GetActiveByBusinessID(businessID uint) ([]*models.AccessKey, error) {
	var accessKeys []*models.AccessKey
	now := time.Now()
	
	err := r.db.Where("business_id = ? AND status = 1 AND (expires_at IS NULL OR expires_at > ?)", 
		businessID, now).Find(&accessKeys).Error
	if err != nil {
		return nil, err
	}
	
	return accessKeys, nil
}

// GetExpiredKeys 获取过期的AK/SK
func (r *AccessKeyRepository) GetExpiredKeys() ([]*models.AccessKey, error) {
	var accessKeys []*models.AccessKey
	now := time.Now()
	
	err := r.db.Where("expires_at IS NOT NULL AND expires_at <= ? AND status = 1", 
		now).Find(&accessKeys).Error
	if err != nil {
		return nil, err
	}
	
	return accessKeys, nil
}

// CountByBusinessID 统计业务的AK/SK数量
func (r *AccessKeyRepository) CountByBusinessID(businessID uint, status *int8) (int64, error) {
	query := r.db.Model(&models.AccessKey{}).Where("business_id = ?", businessID)
	
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	
	var count int64
	err := query.Count(&count).Error
	return count, err
}

// GetByBusinessName 根据业务名称获取AK/SK列表
func (r *AccessKeyRepository) GetByBusinessName(businessName string, status *int8) ([]*models.AccessKey, error) {
	var accessKeys []*models.AccessKey
	
	query := r.db.Joins("JOIN businesses ON businesses.id = access_keys.business_id").
		Where("businesses.name = ?", businessName).
		Preload("Business")
	
	if status != nil {
		query = query.Where("access_keys.status = ?", *status)
	}
	
	err := query.Find(&accessKeys).Error
	if err != nil {
		return nil, err
	}
	
	return accessKeys, nil
}
