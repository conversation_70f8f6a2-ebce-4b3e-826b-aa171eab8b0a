package repository

import (
	"aksk/internal/models"
	"time"
	"gorm.io/gorm"
)

// OperationLogRepository 操作日志数据访问层
type OperationLogRepository struct {
	db *gorm.DB
}

// NewOperationLogRepository 创建操作日志仓库
func NewOperationLogRepository(db *gorm.DB) *OperationLogRepository {
	return &OperationLogRepository{db: db}
}

// Create 创建操作日志
func (r *OperationLogRepository) Create(log *models.OperationLog) error {
	return r.db.Create(log).Error
}

// GetByID 根据ID获取操作日志
func (r *OperationLogRepository) GetByID(id uint) (*models.OperationLog, error) {
	var log models.OperationLog
	err := r.db.Preload("Token").First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// List 获取操作日志列表
func (r *OperationLogRepository) List(offset, limit int, filters *OperationLogFilters) ([]*models.OperationLog, int64, error) {
	var logs []*models.OperationLog
	var total int64

	query := r.db.Model(&models.OperationLog{}).Preload("Token")
	
	// 应用过滤条件
	if filters != nil {
		if filters.TokenID != nil {
			query = query.Where("token_id = ?", *filters.TokenID)
		}
		if filters.Operation != "" {
			query = query.Where("operation = ?", filters.Operation)
		}
		if filters.ResourceType != "" {
			query = query.Where("resource_type = ?", filters.ResourceType)
		}
		if filters.ResourceID != nil {
			query = query.Where("resource_id = ?", *filters.ResourceID)
		}
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.IPAddress != "" {
			query = query.Where("ip_address = ?", filters.IPAddress)
		}
		if !filters.StartTime.IsZero() {
			query = query.Where("created_at >= ?", filters.StartTime)
		}
		if !filters.EndTime.IsZero() {
			query = query.Where("created_at <= ?", filters.EndTime)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// OperationLogFilters 操作日志过滤条件
type OperationLogFilters struct {
	TokenID      *uint     `json:"token_id"`
	Operation    string    `json:"operation"`
	ResourceType string    `json:"resource_type"`
	ResourceID   *uint     `json:"resource_id"`
	Status       *int8     `json:"status"`
	IPAddress    string    `json:"ip_address"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
}

// Delete 删除操作日志
func (r *OperationLogRepository) Delete(id uint) error {
	return r.db.Delete(&models.OperationLog{}, id).Error
}

// DeleteOldLogs 删除旧的操作日志
func (r *OperationLogRepository) DeleteOldLogs(beforeTime time.Time) (int64, error) {
	result := r.db.Where("created_at < ?", beforeTime).Delete(&models.OperationLog{})
	return result.RowsAffected, result.Error
}

// GetByTokenID 根据TokenID获取操作日志
func (r *OperationLogRepository) GetByTokenID(tokenID uint, offset, limit int) ([]*models.OperationLog, int64, error) {
	var logs []*models.OperationLog
	var total int64

	query := r.db.Model(&models.OperationLog{}).Where("token_id = ?", tokenID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetStatistics 获取操作统计信息
func (r *OperationLogRepository) GetStatistics(startTime, endTime time.Time) (map[string]interface{}, error) {
	var results []struct {
		Operation string `json:"operation"`
		Count     int64  `json:"count"`
	}

	err := r.db.Model(&models.OperationLog{}).
		Select("operation, COUNT(*) as count").
		Where("created_at BETWEEN ? AND ?", startTime, endTime).
		Group("operation").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 获取总数
	var total int64
	err = r.db.Model(&models.OperationLog{}).
		Where("created_at BETWEEN ? AND ?", startTime, endTime).
		Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 获取成功/失败统计
	var successCount, failureCount int64
	r.db.Model(&models.OperationLog{}).
		Where("created_at BETWEEN ? AND ? AND status = 1", startTime, endTime).
		Count(&successCount)
	r.db.Model(&models.OperationLog{}).
		Where("created_at BETWEEN ? AND ? AND status = 0", startTime, endTime).
		Count(&failureCount)

	return map[string]interface{}{
		"total":           total,
		"success_count":   successCount,
		"failure_count":   failureCount,
		"operations":      results,
		"success_rate":    float64(successCount) / float64(total) * 100,
	}, nil
}
