package handlers

import (
	"net/http"
	"strconv"

	"aksk/internal/models"
	"aksk/internal/services"

	"github.com/gin-gonic/gin"
)

// AccessKeyHandler AK/SK处理器
type AccessKeyHandler struct {
	accessKeyService *services.AccessKeyService
}

// NewAccessKeyHandler 创建AK/SK处理器
func NewAccessKeyHandler(accessKeyService *services.AccessKeyService) *AccessKeyHandler {
	return &AccessKeyHandler{
		accessKeyService: accessKeyService,
	}
}

// CreateAccessKey 创建AK/SK
// @Summary 创建AK/SK
// @Description 为指定业务创建新的访问密钥
// @Tags access-keys
// @Accept json
// @Produce json
// @Param access_key body models.AccessKeyCreateRequest true "AK/SK信息"
// @Success 201 {object} models.AccessKeyWithSecretResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/v1/access-keys [post]
func (h *AccessKeyHandler) CreateAccessKey(c *gin.Context) {
	var req models.AccessKeyCreateRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	accessKey, err := h.accessKeyService.CreateAccessKey(&req)
	if err != nil {
		if err.Error() == "business not found" || err.Error() == "business is not active" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to create access key",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, accessKey)
}

// GetAccessKey 获取AK/SK详情
// @Summary 获取AK/SK详情
// @Description 根据ID获取访问密钥详情（不包含SecretAccessKey）
// @Tags access-keys
// @Produce json
// @Param id path int true "AK/SK ID"
// @Success 200 {object} models.AccessKeyResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/access-keys/{id} [get]
func (h *AccessKeyHandler) GetAccessKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid access key ID",
		})
		return
	}

	accessKey, err := h.accessKeyService.GetAccessKey(uint(id))
	if err != nil {
		if err.Error() == "access key not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to get access key",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, accessKey)
}

// GetAccessKeyByAccessKeyID 根据AccessKeyID获取AK/SK
// @Summary 根据AccessKeyID获取AK/SK
// @Description 根据AccessKeyID获取访问密钥详情
// @Tags access-keys
// @Produce json
// @Param access_key_id path string true "Access Key ID"
// @Success 200 {object} models.AccessKeyResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/access-keys/by-key/{access_key_id} [get]
func (h *AccessKeyHandler) GetAccessKeyByAccessKeyID(c *gin.Context) {
	accessKeyID := c.Param("access_key_id")
	if accessKeyID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Access Key ID is required",
		})
		return
	}

	accessKey, err := h.accessKeyService.GetAccessKeyByAccessKeyID(accessKeyID)
	if err != nil {
		if err.Error() == "access key not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to get access key",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, accessKey)
}

// ListAccessKeys 获取AK/SK列表
// @Summary 获取AK/SK列表
// @Description 获取访问密钥列表，支持分页和过滤
// @Tags access-keys
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param business_id query int false "业务ID过滤"
// @Param status query int false "状态过滤：1-启用，0-禁用"
// @Success 200 {object} services.PaginatedResponse
// @Router /api/v1/access-keys [get]
func (h *AccessKeyHandler) ListAccessKeys(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	var businessID *uint
	if businessIDStr := c.Query("business_id"); businessIDStr != "" {
		if bid, err := strconv.ParseUint(businessIDStr, 10, 32); err == nil {
			businessIDUint := uint(bid)
			businessID = &businessIDUint
		}
	}
	
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	result, err := h.accessKeyService.ListAccessKeys(page, pageSize, businessID, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to list access keys",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateAccessKey 更新AK/SK
// @Summary 更新AK/SK
// @Description 更新访问密钥信息
// @Tags access-keys
// @Accept json
// @Produce json
// @Param id path int true "AK/SK ID"
// @Param access_key body models.AccessKeyUpdateRequest true "更新信息"
// @Success 200 {object} models.AccessKeyResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/access-keys/{id} [put]
func (h *AccessKeyHandler) UpdateAccessKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid access key ID",
		})
		return
	}

	var req models.AccessKeyUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	accessKey, err := h.accessKeyService.UpdateAccessKey(uint(id), &req)
	if err != nil {
		if err.Error() == "access key not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to update access key",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, accessKey)
}

// DeleteAccessKey 删除AK/SK
// @Summary 删除AK/SK
// @Description 删除访问密钥
// @Tags access-keys
// @Param id path int true "AK/SK ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/access-keys/{id} [delete]
func (h *AccessKeyHandler) DeleteAccessKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid access key ID",
		})
		return
	}

	err = h.accessKeyService.DeleteAccessKey(uint(id))
	if err != nil {
		if err.Error() == "access key not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to delete access key",
			Details: err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetAccessKeysByBusinessName 根据业务名称获取AK/SK
// @Summary 根据业务名称获取AK/SK
// @Description 根据业务名称获取该业务的所有访问密钥
// @Tags access-keys
// @Produce json
// @Param business_name path string true "业务名称"
// @Param status query int false "状态过滤：1-启用，0-禁用"
// @Success 200 {array} models.AccessKeyResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/businesses/{business_name}/access-keys [get]
func (h *AccessKeyHandler) GetAccessKeysByBusinessName(c *gin.Context) {
	businessName := c.Param("business_name")
	if businessName == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Business name is required",
		})
		return
	}
	
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	accessKeys, err := h.accessKeyService.GetAccessKeysByBusinessName(businessName, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to get access keys",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, accessKeys)
}

// ValidateAccessKey 验证AK/SK
// @Summary 验证AK/SK
// @Description 验证访问密钥是否有效
// @Tags access-keys
// @Accept json
// @Produce json
// @Param credentials body ValidateAccessKeyRequest true "AK/SK凭证"
// @Success 200 {object} models.AccessKeyResponse
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/access-keys/validate [post]
func (h *AccessKeyHandler) ValidateAccessKey(c *gin.Context) {
	var req ValidateAccessKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	accessKey, err := h.accessKeyService.ValidateAccessKey(req.AccessKeyID, req.SecretAccessKey)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "Invalid credentials",
		})
		return
	}

	c.JSON(http.StatusOK, accessKey)
}

// ValidateAccessKeyRequest 验证AK/SK请求
type ValidateAccessKeyRequest struct {
	AccessKeyID     string `json:"access_key_id" validate:"required"`
	SecretAccessKey string `json:"secret_access_key" validate:"required"`
}
