package handlers

import (
	"net/http"
	"strconv"

	"aksk/internal/models"
	"aksk/internal/services"

	"github.com/gin-gonic/gin"
)

// BusinessHandler 业务处理器
type BusinessHandler struct {
	businessService *services.BusinessService
}

// NewBusinessHandler 创建业务处理器
func NewBusinessHandler(businessService *services.BusinessService) *BusinessHandler {
	return &BusinessHandler{
		businessService: businessService,
	}
}

// CreateBusiness 创建业务
// @Summary 创建业务
// @Description 创建新的业务
// @Tags businesses
// @Accept json
// @Produce json
// @Param business body models.BusinessCreateRequest true "业务信息"
// @Success 201 {object} models.BusinessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Router /api/v1/businesses [post]
func (h *BusinessHandler) CreateBusiness(c *gin.Context) {
	var req models.BusinessCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	business, err := h.businessService.CreateBusiness(&req)
	if err != nil {
		if err.Error() == "business name already exists" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to create business",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, business)
}

// GetBusiness 获取业务详情
// @Summary 获取业务详情
// @Description 根据ID获取业务详情
// @Tags businesses
// @Produce json
// @Param id path int true "业务ID"
// @Success 200 {object} models.BusinessResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/businesses/{id} [get]
func (h *BusinessHandler) GetBusiness(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid business ID",
		})
		return
	}

	business, err := h.businessService.GetBusiness(uint(id))
	if err != nil {
		if err.Error() == "business not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to get business",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, business)
}

// ListBusinesses 获取业务列表
// @Summary 获取业务列表
// @Description 获取业务列表，支持分页和状态过滤
// @Tags businesses
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query int false "状态过滤：1-启用，0-禁用"
// @Success 200 {object} services.PaginatedResponse
// @Router /api/v1/businesses [get]
func (h *BusinessHandler) ListBusinesses(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			statusInt8 := int8(s)
			status = &statusInt8
		}
	}

	result, err := h.businessService.ListBusinesses(page, pageSize, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to list businesses",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateBusiness 更新业务
// @Summary 更新业务
// @Description 更新业务信息
// @Tags businesses
// @Accept json
// @Produce json
// @Param id path int true "业务ID"
// @Param business body models.BusinessUpdateRequest true "更新信息"
// @Success 200 {object} models.BusinessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/businesses/{id} [put]
func (h *BusinessHandler) UpdateBusiness(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid business ID",
		})
		return
	}

	var req models.BusinessUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Details: err.Error(),
		})
		return
	}

	business, err := h.businessService.UpdateBusiness(uint(id), &req)
	if err != nil {
		if err.Error() == "business not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		if err.Error() == "business name already exists" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to update business",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, business)
}

// DeleteBusiness 删除业务
// @Summary 删除业务
// @Description 删除业务（如果没有关联的访问密钥）
// @Tags businesses
// @Param id path int true "业务ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/businesses/{id} [delete]
func (h *BusinessHandler) DeleteBusiness(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid business ID",
		})
		return
	}

	err = h.businessService.DeleteBusiness(uint(id))
	if err != nil {
		if err.Error() == "business not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		if err.Error() == "cannot delete business with associated access keys" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to delete business",
			Details: err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetBusinessWithAccessKeys 获取业务及其访问密钥
// @Summary 获取业务及其访问密钥
// @Description 获取业务详情及其关联的所有访问密钥
// @Tags businesses
// @Produce json
// @Param id path int true "业务ID"
// @Success 200 {object} BusinessWithAccessKeysResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/businesses/{id}/access-keys [get]
func (h *BusinessHandler) GetBusinessWithAccessKeys(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid business ID",
		})
		return
	}

	business, accessKeys, err := h.businessService.GetBusinessWithAccessKeys(uint(id))
	if err != nil {
		if err.Error() == "business not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: "Failed to get business with access keys",
			Details: err.Error(),
		})
		return
	}

	response := BusinessWithAccessKeysResponse{
		Business:   business,
		AccessKeys: accessKeys,
	}

	c.JSON(http.StatusOK, response)
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Details string `json:"details,omitempty"`
}

// BusinessWithAccessKeysResponse 业务及访问密钥响应
type BusinessWithAccessKeysResponse struct {
	Business   *models.BusinessResponse     `json:"business"`
	AccessKeys []*models.AccessKeyResponse  `json:"access_keys"`
}
