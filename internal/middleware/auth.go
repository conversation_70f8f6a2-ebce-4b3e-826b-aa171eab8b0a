package middleware

import (
	"net/http"
	"strings"
	"time"

	"aksk/internal/models"
	"aksk/internal/repository"
	"aksk/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	tokenRepo *repository.TokenRepository
	logRepo   *repository.OperationLogRepository
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(db *gorm.DB) *AuthMiddleware {
	return &AuthMiddleware{
		tokenRepo: repository.NewTokenRepository(db),
		logRepo:   repository.NewOperationLogRepository(db),
	}
}

// Authenticate Token认证中间件
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Header中获取Token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header is required",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := ""
		if strings.HasPrefix(authHeader, "Bearer ") {
			tokenString = strings.TrimPrefix(authHeader, "Bearer ")
		} else {
			tokenString = authHeader
		}

		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token is required",
			})
			c.Abort()
			return
		}

		// 验证Token
		token, err := m.validateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
			})
			c.Abort()
			return
		}

		// 检查Token是否激活
		if !token.IsActive() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token is inactive or expired",
			})
			c.Abort()
			return
		}

		// 更新最后使用时间
		now := time.Now()
		token.LastUsedAt = &now
		m.tokenRepo.Update(token)

		// 将Token信息存储到上下文中
		c.Set("token", token)
		c.Set("token_id", token.ID)

		c.Next()
	}
}

// RequirePermission 权限检查中间件
func (m *AuthMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token, exists := c.Get("token")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token not found in context",
			})
			c.Abort()
			return
		}

		tokenModel, ok := token.(*models.Token)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid token type in context",
			})
			c.Abort()
			return
		}

		// 检查权限
		if !tokenModel.HasPermission(resource, action) {
			// 记录权限拒绝日志
			m.logPermissionDenied(c, tokenModel, resource, action)
			
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// validateToken 验证Token
func (m *AuthMiddleware) validateToken(tokenString string) (*models.Token, error) {
	// 计算Token哈希
	tokenHash, err := utils.HashPassword(tokenString)
	if err != nil {
		return nil, err
	}

	// 从数据库中查找Token
	token, err := m.tokenRepo.GetByTokenHash(tokenHash)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// logPermissionDenied 记录权限拒绝日志
func (m *AuthMiddleware) logPermissionDenied(c *gin.Context, token *models.Token, resource, action string) {
	logEntry := &models.OperationLog{
		TokenID:      &token.ID,
		Operation:    "permission_denied",
		ResourceType: resource,
		IPAddress:    c.ClientIP(),
		UserAgent:    c.GetHeader("User-Agent"),
		Status:       0, // 失败
	}

	details := &models.LogDetails{
		Extra: map[string]interface{}{
			"requested_action": action,
			"requested_resource": resource,
		},
	}
	logEntry.SetDetails(details)

	m.logRepo.Create(logEntry)
}

// LogOperation 记录操作日志中间件
func (m *AuthMiddleware) LogOperation(operation, resourceType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 执行请求
		c.Next()

		// 获取Token信息
		tokenID, exists := c.Get("token_id")
		if !exists {
			return
		}

		tokenIDUint, ok := tokenID.(uint)
		if !ok {
			return
		}

		// 获取资源ID（如果存在）
		var resourceID *uint
		if id := c.Param("id"); id != "" {
			// 这里可以根据需要解析ID
		}

		// 创建操作日志
		logEntry := &models.OperationLog{
			TokenID:      &tokenIDUint,
			Operation:    operation,
			ResourceType: resourceType,
			ResourceID:   resourceID,
			IPAddress:    c.ClientIP(),
			UserAgent:    c.GetHeader("User-Agent"),
			Status:       1, // 成功
		}

		// 如果响应状态码不是2xx，标记为失败
		if c.Writer.Status() >= 400 {
			logEntry.Status = 0
		}

		m.logRepo.Create(logEntry)
	}
}
