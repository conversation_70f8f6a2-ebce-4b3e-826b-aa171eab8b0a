package middleware

import (
	"net/http"
	"sync"
	"time"

	"aksk/internal/config"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// RateLimiter 限流器
type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rps      int
	burst    int
}

// NewRateLimiter 创建限流器
func NewRateLimiter(cfg *config.RateLimitConfig) *RateLimiter {
	return &RateLimiter{
		limiters: make(map[string]*rate.Limiter),
		rps:      cfg.RPS,
		burst:    cfg.Burst,
	}
}

// getLimiter 获取或创建限流器
func (rl *RateLimiter) getLimiter(key string) *rate.Limiter {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	limiter, exists := rl.limiters[key]
	if !exists {
		limiter = rate.NewLimiter(rate.Limit(rl.rps), rl.burst)
		rl.limiters[key] = limiter
	}

	return limiter
}

// cleanupLimiters 清理过期的限流器
func (rl *RateLimiter) cleanupLimiters() {
	ticker := time.NewTicker(time.Minute * 5)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rl.mu.Lock()
			for key, limiter := range rl.limiters {
				// 如果限流器在过去5分钟内没有被使用，则删除它
				if limiter.Tokens() == float64(rl.burst) {
					delete(rl.limiters, key)
				}
			}
			rl.mu.Unlock()
		}
	}
}

// RateLimit 限流中间件
func (rl *RateLimiter) RateLimit() gin.HandlerFunc {
	// 启动清理协程
	go rl.cleanupLimiters()

	return func(c *gin.Context) {
		// 使用IP地址作为限流键
		key := c.ClientIP()
		
		// 如果有Token，使用Token ID作为限流键
		if tokenID, exists := c.Get("token_id"); exists {
			if tid, ok := tokenID.(uint); ok {
				key = string(rune(tid))
			}
		}

		limiter := rl.getLimiter(key)
		
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"retry_after": "60s",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GlobalRateLimit 全局限流中间件
func GlobalRateLimit(cfg *config.RateLimitConfig) gin.HandlerFunc {
	if !cfg.Enabled {
		return func(c *gin.Context) {
			c.Next()
		}
	}

	rateLimiter := NewRateLimiter(cfg)
	return rateLimiter.RateLimit()
}
