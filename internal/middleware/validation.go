package middleware

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全头
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")
		c<PERSON><PERSON>("X-Frame-Options", "DENY")
		c<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")
		c.<PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.<PERSON><PERSON>("Content-Security-Policy", "default-src 'self'")
		c.<PERSON><PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")
		
		c.Next()
	}
}

// InputValidation 输入验证中间件
func InputValidation() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查请求大小
		if c.Request.ContentLength > 10*1024*1024 { // 10MB
			c.<PERSON>(http.StatusRequestEntityTooLarge, gin.H{
				"error": "Request body too large",
			})
			c.Abort()
			return
		}

		// 检查Content-Type
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			contentType := c.GetHeader("Content-Type")
			if !strings.Contains(contentType, "application/json") {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "Content-Type must be application/json",
				})
				c.Abort()
				return
			}
		}

		// 检查User-Agent
		userAgent := c.GetHeader("User-Agent")
		if userAgent == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "User-Agent header is required",
			})
			c.Abort()
			return
		}

		// 检查可疑的User-Agent
		suspiciousPatterns := []string{
			"sqlmap",
			"nikto",
			"nmap",
			"masscan",
			"nessus",
			"openvas",
			"w3af",
			"dirbuster",
			"gobuster",
		}

		userAgentLower := strings.ToLower(userAgent)
		for _, pattern := range suspiciousPatterns {
			if strings.Contains(userAgentLower, pattern) {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Suspicious User-Agent detected",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// SQLInjectionProtection SQL注入保护中间件
func SQLInjectionProtection() gin.HandlerFunc {
	// SQL注入检测模式
	sqlPatterns := []*regexp.Regexp{
		regexp.MustCompile(`(?i)(union\s+select|select\s+.*\s+from|insert\s+into|update\s+.*\s+set|delete\s+from)`),
		regexp.MustCompile(`(?i)(drop\s+table|create\s+table|alter\s+table|truncate\s+table)`),
		regexp.MustCompile(`(?i)(\'\s*or\s*\'\s*=\s*\'|\'\s*or\s*1\s*=\s*1|--\s*|\/\*.*\*\/)`),
		regexp.MustCompile(`(?i)(exec\s*\(|execute\s*\(|sp_executesql)`),
	}

	return func(c *gin.Context) {
		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsSQLInjection(value, sqlPatterns) {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "Potential SQL injection detected in query parameter: " + key,
					})
					c.Abort()
					return
				}
			}
		}

		// 检查路径参数
		for _, param := range c.Params {
			if containsSQLInjection(param.Value, sqlPatterns) {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Potential SQL injection detected in path parameter: " + param.Key,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// containsSQLInjection 检查字符串是否包含SQL注入模式
func containsSQLInjection(input string, patterns []*regexp.Regexp) bool {
	for _, pattern := range patterns {
		if pattern.MatchString(input) {
			return true
		}
	}
	return false
}

// XSSProtection XSS保护中间件
func XSSProtection() gin.HandlerFunc {
	// XSS检测模式
	xssPatterns := []*regexp.Regexp{
		regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`),
		regexp.MustCompile(`(?i)<iframe[^>]*>.*?</iframe>`),
		regexp.MustCompile(`(?i)<object[^>]*>.*?</object>`),
		regexp.MustCompile(`(?i)<embed[^>]*>.*?</embed>`),
		regexp.MustCompile(`(?i)javascript:`),
		regexp.MustCompile(`(?i)on\w+\s*=`),
		regexp.MustCompile(`(?i)<img[^>]*src\s*=\s*["\']?javascript:`),
	}

	return func(c *gin.Context) {
		// 检查查询参数
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsXSS(value, xssPatterns) {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "Potential XSS detected in query parameter: " + key,
					})
					c.Abort()
					return
				}
			}
		}

		c.Next()
	}
}

// containsXSS 检查字符串是否包含XSS模式
func containsXSS(input string, patterns []*regexp.Regexp) bool {
	for _, pattern := range patterns {
		if pattern.MatchString(input) {
			return true
		}
	}
	return false
}

// IPWhitelist IP白名单中间件
func IPWhitelist(allowedIPs []string) gin.HandlerFunc {
	if len(allowedIPs) == 0 {
		return func(c *gin.Context) {
			c.Next()
		}
	}

	allowedIPMap := make(map[string]bool)
	for _, ip := range allowedIPs {
		allowedIPMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		if !allowedIPMap[clientIP] {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "IP address not allowed",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
