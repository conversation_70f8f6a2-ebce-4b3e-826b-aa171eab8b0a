package services

import (
	"errors"
	"fmt"

	"aksk/internal/models"
	"aksk/internal/repository"
	"aksk/internal/utils"

	"gorm.io/gorm"
)

// AccessKeyService AK/SK服务层
type AccessKeyService struct {
	accessKeyRepo  *repository.AccessKeyRepository
	businessRepo   *repository.BusinessRepository
	cryptoService  *utils.CryptoService
}

// NewAccessKeyService 创建AK/SK服务
func NewAccessKeyService(db *gorm.DB, cryptoService *utils.CryptoService) *AccessKeyService {
	return &AccessKeyService{
		accessKeyRepo: repository.NewAccessKeyRepository(db),
		businessRepo:  repository.NewBusinessRepository(db),
		cryptoService: cryptoService,
	}
}

// CreateAccessKey 创建AK/SK
func (s *AccessKeyService) CreateAccessKey(req *models.AccessKeyCreateRequest) (*models.AccessKeyWithSecretResponse, error) {
	// 检查业务是否存在且激活
	business, err := s.businessRepo.GetByID(req.BusinessID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("business not found")
		}
		return nil, fmt.Errorf("failed to get business: %w", err)
	}
	if !business.IsActive() {
		return nil, errors.New("business is not active")
	}

	// 生成AccessKeyID和SecretAccessKey
	accessKeyID, err := utils.GenerateAccessKeyID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate access key ID: %w", err)
	}

	secretAccessKey, err := utils.GenerateSecretAccessKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate secret access key: %w", err)
	}

	// 加密SecretAccessKey
	encryptedSecret, err := s.cryptoService.Encrypt(secretAccessKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt secret access key: %w", err)
	}

	// 创建AccessKey记录
	accessKey := &models.AccessKey{
		BusinessID:      req.BusinessID,
		AccessKeyID:     accessKeyID,
		SecretAccessKey: encryptedSecret,
		Description:     req.Description,
		Status:          1, // 默认启用
		ExpiresAt:       req.ExpiresAt,
	}

	if err := s.accessKeyRepo.Create(accessKey); err != nil {
		return nil, fmt.Errorf("failed to create access key: %w", err)
	}

	// 重新获取完整信息（包含关联的业务信息）
	accessKey, err = s.accessKeyRepo.GetByID(accessKey.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created access key: %w", err)
	}

	return accessKey.ToResponseWithSecret(secretAccessKey), nil
}

// GetAccessKey 获取AK/SK详情（不包含SecretAccessKey）
func (s *AccessKeyService) GetAccessKey(id uint) (*models.AccessKeyResponse, error) {
	accessKey, err := s.accessKeyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access key not found")
		}
		return nil, fmt.Errorf("failed to get access key: %w", err)
	}

	return accessKey.ToResponse(), nil
}

// GetAccessKeyByAccessKeyID 根据AccessKeyID获取AK/SK
func (s *AccessKeyService) GetAccessKeyByAccessKeyID(accessKeyID string) (*models.AccessKeyResponse, error) {
	accessKey, err := s.accessKeyRepo.GetByAccessKeyID(accessKeyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access key not found")
		}
		return nil, fmt.Errorf("failed to get access key: %w", err)
	}

	return accessKey.ToResponse(), nil
}

// GetSecretAccessKey 获取解密后的SecretAccessKey（仅用于API调用验证）
func (s *AccessKeyService) GetSecretAccessKey(accessKeyID string) (string, error) {
	accessKey, err := s.accessKeyRepo.GetByAccessKeyID(accessKeyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("access key not found")
		}
		return "", fmt.Errorf("failed to get access key: %w", err)
	}

	if !accessKey.IsActive() {
		return "", errors.New("access key is not active or expired")
	}

	// 解密SecretAccessKey
	secretAccessKey, err := s.cryptoService.Decrypt(accessKey.SecretAccessKey)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt secret access key: %w", err)
	}

	return secretAccessKey, nil
}

// ListAccessKeys 获取AK/SK列表
func (s *AccessKeyService) ListAccessKeys(page, pageSize int, businessID *uint, status *int8) (*PaginatedResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	accessKeys, total, err := s.accessKeyRepo.List(offset, pageSize, businessID, status)
	if err != nil {
		return nil, fmt.Errorf("failed to list access keys: %w", err)
	}

	// 转换为响应格式
	var items []interface{}
	for _, accessKey := range accessKeys {
		items = append(items, accessKey.ToResponse())
	}

	return &PaginatedResponse{
		Items:    items,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// UpdateAccessKey 更新AK/SK
func (s *AccessKeyService) UpdateAccessKey(id uint, req *models.AccessKeyUpdateRequest) (*models.AccessKeyResponse, error) {
	// 获取现有AK/SK
	accessKey, err := s.accessKeyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access key not found")
		}
		return nil, fmt.Errorf("failed to get access key: %w", err)
	}

	// 更新字段
	if req.Description != "" {
		accessKey.Description = req.Description
	}
	if req.Status != nil {
		accessKey.Status = *req.Status
	}
	if req.ExpiresAt != nil {
		accessKey.ExpiresAt = req.ExpiresAt
	}

	// 保存更新
	if err := s.accessKeyRepo.Update(accessKey); err != nil {
		return nil, fmt.Errorf("failed to update access key: %w", err)
	}

	return accessKey.ToResponse(), nil
}

// DeleteAccessKey 删除AK/SK
func (s *AccessKeyService) DeleteAccessKey(id uint) error {
	// 检查AK/SK是否存在
	exists, err := s.accessKeyRepo.Exists(id)
	if err != nil {
		return fmt.Errorf("failed to check access key existence: %w", err)
	}
	if !exists {
		return errors.New("access key not found")
	}

	// 删除AK/SK
	if err := s.accessKeyRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete access key: %w", err)
	}

	return nil
}

// GetAccessKeysByBusinessName 根据业务名称获取AK/SK列表
func (s *AccessKeyService) GetAccessKeysByBusinessName(businessName string, status *int8) ([]*models.AccessKeyResponse, error) {
	accessKeys, err := s.accessKeyRepo.GetByBusinessName(businessName, status)
	if err != nil {
		return nil, fmt.Errorf("failed to get access keys by business name: %w", err)
	}

	// 转换为响应格式
	var responses []*models.AccessKeyResponse
	for _, accessKey := range accessKeys {
		responses = append(responses, accessKey.ToResponse())
	}

	return responses, nil
}

// ValidateAccessKey 验证AK/SK是否有效
func (s *AccessKeyService) ValidateAccessKey(accessKeyID, secretAccessKey string) (*models.AccessKeyResponse, error) {
	// 获取AK/SK记录
	accessKey, err := s.accessKeyRepo.GetByAccessKeyID(accessKeyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid access key")
		}
		return nil, fmt.Errorf("failed to get access key: %w", err)
	}

	// 检查是否激活
	if !accessKey.IsActive() {
		return nil, errors.New("access key is not active or expired")
	}

	// 解密并验证SecretAccessKey
	decryptedSecret, err := s.cryptoService.Decrypt(accessKey.SecretAccessKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt secret access key: %w", err)
	}

	if decryptedSecret != secretAccessKey {
		return nil, errors.New("invalid secret access key")
	}

	return accessKey.ToResponse(), nil
}
