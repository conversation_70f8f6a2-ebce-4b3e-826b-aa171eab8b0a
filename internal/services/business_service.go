package services

import (
	"errors"
	"fmt"

	"aksk/internal/models"
	"aksk/internal/repository"

	"gorm.io/gorm"
)

// BusinessService 业务服务层
type BusinessService struct {
	businessRepo   *repository.BusinessRepository
	accessKeyRepo  *repository.AccessKeyRepository
	operationLogRepo *repository.OperationLogRepository
}

// NewBusinessService 创建业务服务
func NewBusinessService(db *gorm.DB) *BusinessService {
	return &BusinessService{
		businessRepo:   repository.NewBusinessRepository(db),
		accessKeyRepo:  repository.NewAccessKeyRepository(db),
		operationLogRepo: repository.NewOperationLogRepository(db),
	}
}

// CreateBusiness 创建业务
func (s *BusinessService) CreateBusiness(req *models.BusinessCreateRequest) (*models.BusinessResponse, error) {
	// 检查业务名称是否已存在
	exists, err := s.businessRepo.ExistsByName(req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to check business name existence: %w", err)
	}
	if exists {
		return nil, errors.New("business name already exists")
	}

	// 创建业务
	business := &models.Business{
		Name:        req.Name,
		Description: req.Description,
		Status:      1, // 默认启用
	}

	if err := s.businessRepo.Create(business); err != nil {
		return nil, fmt.Errorf("failed to create business: %w", err)
	}

	return business.ToResponse(), nil
}

// GetBusiness 获取业务详情
func (s *BusinessService) GetBusiness(id uint) (*models.BusinessResponse, error) {
	business, err := s.businessRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("business not found")
		}
		return nil, fmt.Errorf("failed to get business: %w", err)
	}

	return business.ToResponse(), nil
}

// ListBusinesses 获取业务列表
func (s *BusinessService) ListBusinesses(page, pageSize int, status *int8) (*PaginatedResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	businesses, total, err := s.businessRepo.List(offset, pageSize, status)
	if err != nil {
		return nil, fmt.Errorf("failed to list businesses: %w", err)
	}

	// 转换为响应格式
	var items []interface{}
	for _, business := range businesses {
		items = append(items, business.ToResponse())
	}

	return &PaginatedResponse{
		Items:    items,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// UpdateBusiness 更新业务
func (s *BusinessService) UpdateBusiness(id uint, req *models.BusinessUpdateRequest) (*models.BusinessResponse, error) {
	// 获取现有业务
	business, err := s.businessRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("business not found")
		}
		return nil, fmt.Errorf("failed to get business: %w", err)
	}

	// 检查名称是否重复
	if req.Name != "" && req.Name != business.Name {
		exists, err := s.businessRepo.ExistsByName(req.Name, id)
		if err != nil {
			return nil, fmt.Errorf("failed to check business name existence: %w", err)
		}
		if exists {
			return nil, errors.New("business name already exists")
		}
		business.Name = req.Name
	}

	// 更新字段
	if req.Description != "" {
		business.Description = req.Description
	}
	if req.Status != nil {
		business.Status = *req.Status
	}

	// 保存更新
	if err := s.businessRepo.Update(business); err != nil {
		return nil, fmt.Errorf("failed to update business: %w", err)
	}

	return business.ToResponse(), nil
}

// DeleteBusiness 删除业务
func (s *BusinessService) DeleteBusiness(id uint) error {
	// 检查业务是否存在
	exists, err := s.businessRepo.Exists(id)
	if err != nil {
		return fmt.Errorf("failed to check business existence: %w", err)
	}
	if !exists {
		return errors.New("business not found")
	}

	// 检查是否有关联的访问密钥
	count, err := s.accessKeyRepo.CountByBusinessID(id, nil)
	if err != nil {
		return fmt.Errorf("failed to check associated access keys: %w", err)
	}
	if count > 0 {
		return errors.New("cannot delete business with associated access keys")
	}

	// 删除业务
	if err := s.businessRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete business: %w", err)
	}

	return nil
}

// GetBusinessWithAccessKeys 获取业务及其关联的访问密钥
func (s *BusinessService) GetBusinessWithAccessKeys(id uint) (*models.BusinessResponse, []*models.AccessKeyResponse, error) {
	// 获取业务信息
	business, err := s.businessRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, errors.New("business not found")
		}
		return nil, nil, fmt.Errorf("failed to get business: %w", err)
	}

	// 获取关联的访问密钥
	accessKeys, _, err := s.accessKeyRepo.List(0, 1000, &id, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get access keys: %w", err)
	}

	// 转换为响应格式
	var accessKeyResponses []*models.AccessKeyResponse
	for _, ak := range accessKeys {
		accessKeyResponses = append(accessKeyResponses, ak.ToResponse())
	}

	return business.ToResponse(), accessKeyResponses, nil
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	Items    []interface{} `json:"items"`
	Total    int64         `json:"total"`
	Page     int           `json:"page"`
	PageSize int           `json:"page_size"`
}
