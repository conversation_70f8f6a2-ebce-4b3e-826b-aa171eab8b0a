package services

import (
	"errors"
	"fmt"

	"aksk/internal/models"
	"aksk/internal/repository"
	"aksk/internal/utils"

	"gorm.io/gorm"
)

// TokenService Token服务层
type TokenService struct {
	tokenRepo *repository.TokenRepository
}

// NewTokenService 创建Token服务
func NewTokenService(db *gorm.DB) *TokenService {
	return &TokenService{
		tokenRepo: repository.NewTokenRepository(db),
	}
}

// CreateToken 创建Token
func (s *TokenService) CreateToken(req *models.TokenCreateRequest) (*models.TokenWithValueResponse, error) {
	// 检查Token名称是否已存在
	exists, err := s.tokenRepo.ExistsByName(req.TokenName)
	if err != nil {
		return nil, fmt.Errorf("failed to check token name existence: %w", err)
	}
	if exists {
		return nil, errors.New("token name already exists")
	}

	// 生成Token值
	tokenValue, err := utils.GenerateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 计算Token哈希
	tokenHash, err := utils.HashPassword(tokenValue)
	if err != nil {
		return nil, fmt.Errorf("failed to hash token: %w", err)
	}

	// 创建Token记录
	token := &models.Token{
		TokenName: req.TokenName,
		TokenHash: tokenHash,
		Status:    1, // 默认启用
		ExpiresAt: req.ExpiresAt,
	}

	// 设置权限
	if err := token.SetPermissions(req.Permissions); err != nil {
		return nil, fmt.Errorf("failed to set permissions: %w", err)
	}

	if err := s.tokenRepo.Create(token); err != nil {
		return nil, fmt.Errorf("failed to create token: %w", err)
	}

	// 返回包含Token值的响应
	return token.ToResponseWithValue(tokenValue)
}

// GetToken 获取Token详情
func (s *TokenService) GetToken(id uint) (*models.TokenResponse, error) {
	token, err := s.tokenRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("token not found")
		}
		return nil, fmt.Errorf("failed to get token: %w", err)
	}

	return token.ToResponse()
}

// ListTokens 获取Token列表
func (s *TokenService) ListTokens(page, pageSize int, status *int8) (*PaginatedResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	tokens, total, err := s.tokenRepo.List(offset, pageSize, status)
	if err != nil {
		return nil, fmt.Errorf("failed to list tokens: %w", err)
	}

	// 转换为响应格式
	var items []interface{}
	for _, token := range tokens {
		resp, err := token.ToResponse()
		if err != nil {
			return nil, fmt.Errorf("failed to convert token to response: %w", err)
		}
		items = append(items, resp)
	}

	return &PaginatedResponse{
		Items:    items,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// UpdateToken 更新Token
func (s *TokenService) UpdateToken(id uint, req *models.TokenUpdateRequest) (*models.TokenResponse, error) {
	// 获取现有Token
	token, err := s.tokenRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("token not found")
		}
		return nil, fmt.Errorf("failed to get token: %w", err)
	}

	// 检查名称是否重复
	if req.TokenName != "" && req.TokenName != token.TokenName {
		exists, err := s.tokenRepo.ExistsByName(req.TokenName, id)
		if err != nil {
			return nil, fmt.Errorf("failed to check token name existence: %w", err)
		}
		if exists {
			return nil, errors.New("token name already exists")
		}
		token.TokenName = req.TokenName
	}

	// 更新字段
	if req.Permissions != nil {
		if err := token.SetPermissions(req.Permissions); err != nil {
			return nil, fmt.Errorf("failed to set permissions: %w", err)
		}
	}
	if req.Status != nil {
		token.Status = *req.Status
	}
	if req.ExpiresAt != nil {
		token.ExpiresAt = req.ExpiresAt
	}

	// 保存更新
	if err := s.tokenRepo.Update(token); err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	return token.ToResponse()
}

// DeleteToken 删除Token
func (s *TokenService) DeleteToken(id uint) error {
	// 检查Token是否存在
	exists, err := s.tokenRepo.Exists(id)
	if err != nil {
		return fmt.Errorf("failed to check token existence: %w", err)
	}
	if !exists {
		return errors.New("token not found")
	}

	// 删除Token
	if err := s.tokenRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete token: %w", err)
	}

	return nil
}

// ValidateToken 验证Token
func (s *TokenService) ValidateToken(tokenValue string) (*models.TokenResponse, error) {
	// 获取所有活跃的Token
	tokens, err := s.tokenRepo.GetActiveTokens()
	if err != nil {
		return nil, fmt.Errorf("failed to get active tokens: %w", err)
	}

	// 遍历所有Token，检查哈希是否匹配
	for _, token := range tokens {
		if utils.CheckPasswordHash(tokenValue, token.TokenHash) {
			// 检查Token是否激活
			if !token.IsActive() {
				return nil, errors.New("token is inactive or expired")
			}
			return token.ToResponse()
		}
	}

	return nil, errors.New("invalid token")
}

// RegenerateToken 重新生成Token值
func (s *TokenService) RegenerateToken(id uint) (*models.TokenWithValueResponse, error) {
	// 获取现有Token
	token, err := s.tokenRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("token not found")
		}
		return nil, fmt.Errorf("failed to get token: %w", err)
	}

	// 生成新的Token值
	tokenValue, err := utils.GenerateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 计算新的Token哈希
	tokenHash, err := utils.HashPassword(tokenValue)
	if err != nil {
		return nil, fmt.Errorf("failed to hash token: %w", err)
	}

	// 更新Token哈希
	token.TokenHash = tokenHash

	if err := s.tokenRepo.Update(token); err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	// 返回包含新Token值的响应
	return token.ToResponseWithValue(tokenValue)
}
