package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"aksk/internal/config"
	"aksk/internal/handlers"
	"aksk/internal/middleware"
	"aksk/internal/models"
	"aksk/internal/repository"
	"aksk/internal/services"
	"aksk/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type IntegrationTestSuite struct {
	suite.Suite
	db           *gorm.DB
	router       *gin.Engine
	cryptoService *utils.CryptoService
	testToken    string
}

func (suite *IntegrationTestSuite) SetupSuite() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = db.AutoMigrate(
		&models.Business{},
		&models.AccessKey{},
		&models.Token{},
		&models.OperationLog{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// 初始化加密服务
	suite.cryptoService = utils.NewCryptoService("test-encryption-key-32-bytes!!")

	// 初始化服务
	businessService := services.NewBusinessService(db)
	accessKeyService := services.NewAccessKeyService(db, suite.cryptoService)
	tokenService := services.NewTokenService(db)

	// 初始化处理器
	businessHandler := handlers.NewBusinessHandler(businessService)
	accessKeyHandler := handlers.NewAccessKeyHandler(accessKeyService)

	// 初始化中间件
	authMiddleware := middleware.NewAuthMiddleware(db)

	// 创建路由器
	router := gin.New()
	router.Use(gin.Recovery())

	// 设置路由
	v1 := router.Group("/api/v1")
	{
		// 公开端点
		v1.POST("/access-keys/validate", accessKeyHandler.ValidateAccessKey)

		// 需要认证的端点
		protected := v1.Group("")
		protected.Use(authMiddleware.Authenticate())
		{
			// 业务管理
			businesses := protected.Group("/businesses")
			businesses.Use(authMiddleware.RequirePermission("businesses", "read"))
			{
				businesses.GET("", businessHandler.ListBusinesses)
				businesses.GET("/:id", businessHandler.GetBusiness)
				
				businessesWrite := businesses.Group("")
				businessesWrite.Use(authMiddleware.RequirePermission("businesses", "write"))
				{
					businessesWrite.POST("", businessHandler.CreateBusiness)
					businessesWrite.PUT("/:id", businessHandler.UpdateBusiness)
					businessesWrite.DELETE("/:id", businessHandler.DeleteBusiness)
				}
			}

			// AK/SK管理
			accessKeys := protected.Group("/access-keys")
			accessKeys.Use(authMiddleware.RequirePermission("access_keys", "read"))
			{
				accessKeys.GET("", accessKeyHandler.ListAccessKeys)
				accessKeys.GET("/:id", accessKeyHandler.GetAccessKey)
				
				accessKeysWrite := accessKeys.Group("")
				accessKeysWrite.Use(authMiddleware.RequirePermission("access_keys", "write"))
				{
					accessKeysWrite.POST("", accessKeyHandler.CreateAccessKey)
					accessKeysWrite.PUT("/:id", accessKeyHandler.UpdateAccessKey)
					accessKeysWrite.DELETE("/:id", accessKeyHandler.DeleteAccessKey)
				}
			}
		}
	}

	suite.router = router

	// 创建测试Token
	suite.createTestToken(tokenService)
}

func (suite *IntegrationTestSuite) createTestToken(tokenService *services.TokenService) {
	permissions := &models.Permission{
		Businesses: []string{"read", "write"},
		AccessKeys: []string{"read", "write"},
		Tokens:     []string{"read", "write"},
	}

	req := &models.TokenCreateRequest{
		TokenName:   "test-token",
		Permissions: permissions,
	}

	tokenResp, err := tokenService.CreateToken(req)
	suite.Require().NoError(err)
	suite.testToken = tokenResp.TokenValue
}

func (suite *IntegrationTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *IntegrationTestSuite) makeRequest(method, url string, body interface{}, token string) *httptest.ResponseRecorder {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}

	req, _ := http.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

func (suite *IntegrationTestSuite) TestBusinessWorkflow() {
	// 1. 创建业务
	createReq := models.BusinessCreateRequest{
		Name:        "test-integration-business",
		Description: "Integration test business",
	}

	w := suite.makeRequest("POST", "/api/v1/businesses", createReq, suite.testToken)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var business models.BusinessResponse
	err := json.Unmarshal(w.Body.Bytes(), &business)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), createReq.Name, business.Name)

	// 2. 获取业务列表
	w = suite.makeRequest("GET", "/api/v1/businesses", nil, suite.testToken)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var listResp services.PaginatedResponse
	err = json.Unmarshal(w.Body.Bytes(), &listResp)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), listResp.Total > 0)

	// 3. 获取业务详情
	w = suite.makeRequest("GET", "/api/v1/businesses/1", nil, suite.testToken)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 4. 更新业务
	updateReq := models.BusinessUpdateRequest{
		Description: "Updated description",
	}

	w = suite.makeRequest("PUT", "/api/v1/businesses/1", updateReq, suite.testToken)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *IntegrationTestSuite) TestAccessKeyWorkflow() {
	// 先创建一个业务
	createBusinessReq := models.BusinessCreateRequest{
		Name:        "ak-test-business",
		Description: "Business for AK test",
	}

	w := suite.makeRequest("POST", "/api/v1/businesses", createBusinessReq, suite.testToken)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var business models.BusinessResponse
	err := json.Unmarshal(w.Body.Bytes(), &business)
	assert.NoError(suite.T(), err)

	// 1. 创建访问密钥
	createAKReq := models.AccessKeyCreateRequest{
		BusinessID:  business.ID,
		Description: "Test access key",
	}

	w = suite.makeRequest("POST", "/api/v1/access-keys", createAKReq, suite.testToken)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var accessKey models.AccessKeyWithSecretResponse
	err = json.Unmarshal(w.Body.Bytes(), &accessKey)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), accessKey.AccessKeyID)
	assert.NotEmpty(suite.T(), accessKey.SecretAccessKey)

	// 2. 验证访问密钥
	validateReq := map[string]string{
		"access_key_id":     accessKey.AccessKeyID,
		"secret_access_key": accessKey.SecretAccessKey,
	}

	w = suite.makeRequest("POST", "/api/v1/access-keys/validate", validateReq, "")
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 3. 获取访问密钥列表
	w = suite.makeRequest("GET", "/api/v1/access-keys", nil, suite.testToken)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var akListResp services.PaginatedResponse
	err = json.Unmarshal(w.Body.Bytes(), &akListResp)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), akListResp.Total > 0)

	// 4. 获取访问密钥详情
	w = suite.makeRequest("GET", "/api/v1/access-keys/1", nil, suite.testToken)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *IntegrationTestSuite) TestUnauthorizedAccess() {
	// 不提供Token
	w := suite.makeRequest("GET", "/api/v1/businesses", nil, "")
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	// 提供无效Token
	w = suite.makeRequest("GET", "/api/v1/businesses", nil, "invalid-token")
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *IntegrationTestSuite) TestValidationErrors() {
	// 创建业务时缺少必要字段
	invalidReq := map[string]string{
		"description": "Missing name field",
	}

	w := suite.makeRequest("POST", "/api/v1/businesses", invalidReq, suite.testToken)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

func TestIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}
