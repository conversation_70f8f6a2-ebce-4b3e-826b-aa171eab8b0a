package tests

import (
	"testing"

	"aksk/internal/utils"

	"github.com/stretchr/testify/assert"
)

func TestCryptoService(t *testing.T) {
	cryptoService := utils.NewCryptoService("test-encryption-key-32-bytes!!")

	t.Run("Encrypt and Decrypt", func(t *testing.T) {
		plaintext := "this is a secret message"
		
		// 加密
		encrypted, err := cryptoService.Encrypt(plaintext)
		assert.NoError(t, err)
		assert.NotEmpty(t, encrypted)
		assert.NotEqual(t, plaintext, encrypted)

		// 解密
		decrypted, err := cryptoService.Decrypt(encrypted)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decrypted)
	})

	t.Run("Encrypt Different Results", func(t *testing.T) {
		plaintext := "same message"
		
		encrypted1, err := cryptoService.Encrypt(plaintext)
		assert.NoError(t, err)
		
		encrypted2, err := cryptoService.Encrypt(plaintext)
		assert.NoError(t, err)
		
		// 由于使用随机nonce，相同明文的加密结果应该不同
		assert.NotEqual(t, encrypted1, encrypted2)
		
		// 但解密结果应该相同
		decrypted1, err := cryptoService.Decrypt(encrypted1)
		assert.NoError(t, err)
		
		decrypted2, err := cryptoService.Decrypt(encrypted2)
		assert.NoError(t, err)
		
		assert.Equal(t, plaintext, decrypted1)
		assert.Equal(t, plaintext, decrypted2)
	})

	t.Run("Decrypt Invalid Data", func(t *testing.T) {
		_, err := cryptoService.Decrypt("invalid-encrypted-data")
		assert.Error(t, err)
	})
}

func TestHashPassword(t *testing.T) {
	password := "test-password-123"
	
	// 生成哈希
	hash, err := utils.HashPassword(password)
	assert.NoError(t, err)
	assert.NotEmpty(t, hash)
	assert.NotEqual(t, password, hash)

	// 验证密码
	isValid := utils.CheckPasswordHash(password, hash)
	assert.True(t, isValid)

	// 验证错误密码
	isValid = utils.CheckPasswordHash("wrong-password", hash)
	assert.False(t, isValid)
}

func TestGenerateRandomString(t *testing.T) {
	length := 20
	
	str1, err := utils.GenerateRandomString(length)
	assert.NoError(t, err)
	assert.Len(t, str1, length)

	str2, err := utils.GenerateRandomString(length)
	assert.NoError(t, err)
	assert.Len(t, str2, length)

	// 两次生成的字符串应该不同
	assert.NotEqual(t, str1, str2)
}

func TestGenerateAccessKeyID(t *testing.T) {
	akid1, err := utils.GenerateAccessKeyID()
	assert.NoError(t, err)
	assert.NotEmpty(t, akid1)
	assert.True(t, len(akid1) >= 20) // AK + 18字符
	assert.HasPrefix(t, akid1, "AK")

	akid2, err := utils.GenerateAccessKeyID()
	assert.NoError(t, err)
	assert.NotEqual(t, akid1, akid2)
}

func TestGenerateSecretAccessKey(t *testing.T) {
	sk1, err := utils.GenerateSecretAccessKey()
	assert.NoError(t, err)
	assert.NotEmpty(t, sk1)

	sk2, err := utils.GenerateSecretAccessKey()
	assert.NoError(t, err)
	assert.NotEqual(t, sk1, sk2)
}

func TestGenerateToken(t *testing.T) {
	token1, err := utils.GenerateToken()
	assert.NoError(t, err)
	assert.NotEmpty(t, token1)

	token2, err := utils.GenerateToken()
	assert.NoError(t, err)
	assert.NotEqual(t, token1, token2)
}
