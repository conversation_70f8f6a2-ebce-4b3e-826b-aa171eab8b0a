package tests

import (
	"testing"

	"aksk/internal/models"
	"aksk/internal/services"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type BusinessServiceTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service *services.BusinessService
}

func (suite *BusinessServiceTestSuite) SetupTest() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = db.AutoMigrate(&models.Business{}, &models.AccessKey{})
	suite.Require().NoError(err)

	suite.db = db
	suite.service = services.NewBusinessService(db)
}

func (suite *BusinessServiceTestSuite) TearDownTest() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *BusinessServiceTestSuite) TestCreateBusiness() {
	req := &models.BusinessCreateRequest{
		Name:        "test-business",
		Description: "Test business description",
	}

	business, err := suite.service.CreateBusiness(req)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), business)
	assert.Equal(suite.T(), req.Name, business.Name)
	assert.Equal(suite.T(), req.Description, business.Description)
	assert.Equal(suite.T(), int8(1), business.Status)
}

func (suite *BusinessServiceTestSuite) TestCreateBusinessDuplicateName() {
	req := &models.BusinessCreateRequest{
		Name:        "duplicate-business",
		Description: "Test business description",
	}

	// 创建第一个业务
	_, err := suite.service.CreateBusiness(req)
	assert.NoError(suite.T(), err)

	// 尝试创建同名业务
	_, err = suite.service.CreateBusiness(req)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "business name already exists")
}

func (suite *BusinessServiceTestSuite) TestGetBusiness() {
	// 先创建一个业务
	req := &models.BusinessCreateRequest{
		Name:        "get-test-business",
		Description: "Test business for get operation",
	}
	created, err := suite.service.CreateBusiness(req)
	assert.NoError(suite.T(), err)

	// 获取业务
	business, err := suite.service.GetBusiness(created.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), business)
	assert.Equal(suite.T(), created.ID, business.ID)
	assert.Equal(suite.T(), created.Name, business.Name)
}

func (suite *BusinessServiceTestSuite) TestGetBusinessNotFound() {
	business, err := suite.service.GetBusiness(999)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), business)
	assert.Contains(suite.T(), err.Error(), "business not found")
}

func (suite *BusinessServiceTestSuite) TestListBusinesses() {
	// 创建多个业务
	businesses := []models.BusinessCreateRequest{
		{Name: "business1", Description: "Description 1"},
		{Name: "business2", Description: "Description 2"},
		{Name: "business3", Description: "Description 3"},
	}

	for _, req := range businesses {
		_, err := suite.service.CreateBusiness(&req)
		assert.NoError(suite.T(), err)
	}

	// 获取业务列表
	result, err := suite.service.ListBusinesses(1, 10, nil)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(3), result.Total)
	assert.Len(suite.T(), result.Items, 3)
}

func (suite *BusinessServiceTestSuite) TestUpdateBusiness() {
	// 先创建一个业务
	req := &models.BusinessCreateRequest{
		Name:        "update-test-business",
		Description: "Original description",
	}
	created, err := suite.service.CreateBusiness(req)
	assert.NoError(suite.T(), err)

	// 更新业务
	updateReq := &models.BusinessUpdateRequest{
		Name:        "updated-business",
		Description: "Updated description",
	}
	
	updated, err := suite.service.UpdateBusiness(created.ID, updateReq)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), updated)
	assert.Equal(suite.T(), updateReq.Name, updated.Name)
	assert.Equal(suite.T(), updateReq.Description, updated.Description)
}

func (suite *BusinessServiceTestSuite) TestUpdateBusinessNotFound() {
	updateReq := &models.BusinessUpdateRequest{
		Name: "non-existent-business",
	}
	
	updated, err := suite.service.UpdateBusiness(999, updateReq)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), updated)
	assert.Contains(suite.T(), err.Error(), "business not found")
}

func (suite *BusinessServiceTestSuite) TestDeleteBusiness() {
	// 先创建一个业务
	req := &models.BusinessCreateRequest{
		Name:        "delete-test-business",
		Description: "Business to be deleted",
	}
	created, err := suite.service.CreateBusiness(req)
	assert.NoError(suite.T(), err)

	// 删除业务
	err = suite.service.DeleteBusiness(created.ID)
	assert.NoError(suite.T(), err)

	// 验证业务已被删除
	_, err = suite.service.GetBusiness(created.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "business not found")
}

func (suite *BusinessServiceTestSuite) TestDeleteBusinessNotFound() {
	err := suite.service.DeleteBusiness(999)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "business not found")
}

func TestBusinessServiceTestSuite(t *testing.T) {
	suite.Run(t, new(BusinessServiceTestSuite))
}
