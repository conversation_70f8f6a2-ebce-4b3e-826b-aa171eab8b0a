# AK/SK 管理系统 Makefile

.PHONY: help build test clean run docker-build docker-run migrate lint fmt vet deps

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
APP_NAME := aksk
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 帮助信息
help: ## 显示帮助信息
	@echo "AK/SK 管理系统构建工具"
	@echo ""
	@echo "可用命令:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 安装依赖
deps: ## 安装项目依赖
	@echo "安装依赖..."
	go mod download
	go mod tidy

# 代码格式化
fmt: ## 格式化代码
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
vet: ## 运行 go vet
	@echo "运行代码检查..."
	go vet ./...

# 代码质量检查
lint: ## 运行 golangci-lint
	@echo "运行代码质量检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，跳过检查"; \
	fi

# 运行测试
test: ## 运行所有测试
	@echo "运行测试..."
	go test -v -race -coverprofile=coverage.out ./...

# 运行测试并生成覆盖率报告
test-coverage: test ## 运行测试并生成覆盖率报告
	@echo "生成覆盖率报告..."
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 运行基准测试
bench: ## 运行基准测试
	@echo "运行基准测试..."
	go test -bench=. -benchmem ./...

# 构建应用
build: deps fmt vet ## 构建应用
	@echo "构建应用..."
	go build $(LDFLAGS) -o bin/$(APP_NAME) cmd/server/main.go
	go build $(LDFLAGS) -o bin/migrate cmd/migrate/main.go

# 构建所有平台
build-all: deps fmt vet ## 构建所有平台的二进制文件
	@echo "构建所有平台..."
	@mkdir -p dist
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-linux-amd64 cmd/server/main.go
	GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o dist/$(APP_NAME)-linux-arm64 cmd/server/main.go
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-darwin-amd64 cmd/server/main.go
	GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o dist/$(APP_NAME)-darwin-arm64 cmd/server/main.go
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-windows-amd64.exe cmd/server/main.go

# 运行应用
run: build ## 构建并运行应用
	@echo "运行应用..."
	./bin/$(APP_NAME)

# 运行数据库迁移
migrate: ## 运行数据库迁移
	@echo "运行数据库迁移..."
	@if [ -f bin/migrate ]; then \
		./bin/migrate; \
	else \
		go run cmd/migrate/main.go; \
	fi

# 清理构建文件
clean: ## 清理构建文件
	@echo "清理构建文件..."
	rm -rf bin/
	rm -rf dist/
	rm -f coverage.out coverage.html

# Docker 构建
docker-build: ## 构建 Docker 镜像
	@echo "构建 Docker 镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker 运行
docker-run: ## 运行 Docker 容器
	@echo "运行 Docker 容器..."
	docker-compose up -d

# Docker 停止
docker-stop: ## 停止 Docker 容器
	@echo "停止 Docker 容器..."
	docker-compose down

# Docker 清理
docker-clean: ## 清理 Docker 资源
	@echo "清理 Docker 资源..."
	docker-compose down -v --remove-orphans
	docker system prune -f

# 开发环境设置
dev-setup: deps ## 设置开发环境
	@echo "设置开发环境..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "已创建 .env 文件，请根据需要修改配置"; \
	fi
	@if [ ! -f config/config.yaml ]; then \
		cp config/config.example.yaml config/config.yaml; \
		echo "已创建配置文件，请根据需要修改配置"; \
	fi

# 代码质量检查（完整）
quality: fmt vet lint test ## 运行完整的代码质量检查

# 发布准备
release: clean quality build-all ## 准备发布版本
	@echo "准备发布版本 $(VERSION)..."
	@echo "构建完成，文件位于 dist/ 目录"

# 安装开发工具
install-tools: ## 安装开发工具
	@echo "安装开发工具..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# 生成 API 文档
docs: ## 生成 API 文档
	@echo "生成 API 文档..."
	@if command -v swag >/dev/null 2>&1; then \
		swag init -g cmd/server/main.go -o docs/swagger; \
	else \
		echo "swag 未安装，请运行 make install-tools"; \
	fi

# 监控模式运行（需要安装 air）
dev: ## 开发模式运行（热重载）
	@echo "开发模式运行..."
	@if command -v air >/dev/null 2>&1; then \
		air; \
	else \
		echo "air 未安装，使用普通模式运行..."; \
		make run; \
	fi

# 安装 air（热重载工具）
install-air: ## 安装 air 热重载工具
	@echo "安装 air..."
	go install github.com/cosmtrek/air@latest

# 数据库相关
db-reset: ## 重置数据库
	@echo "重置数据库..."
	docker-compose exec mysql mysql -u root -p -e "DROP DATABASE IF EXISTS aksk; CREATE DATABASE aksk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
	make migrate

# 查看日志
logs: ## 查看应用日志
	@echo "查看应用日志..."
	docker-compose logs -f aksk-app

# 健康检查
health: ## 检查应用健康状态
	@echo "检查应用健康状态..."
	@curl -f http://localhost:8080/health || echo "应用未运行或健康检查失败"

# 显示版本信息
version: ## 显示版本信息
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Go 版本: $(GO_VERSION)"
