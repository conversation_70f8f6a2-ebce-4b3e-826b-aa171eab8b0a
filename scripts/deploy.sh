#!/bin/bash

# AK/SK 管理系统部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs/nginx
    mkdir -p docker/nginx/ssl
    
    log_info "目录创建完成"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    if [ ! -f .env ]; then
        log_info "创建 .env 文件..."
        cp .env.example .env
        
        # 生成随机密钥
        ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
        JWT_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
        DB_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-16)
        
        # 更新 .env 文件
        sed -i "s/your-32-byte-encryption-key-here!!/${ENCRYPTION_KEY}/" .env
        sed -i "s/your-jwt-secret-key-here/${JWT_SECRET}/" .env
        sed -i "s/your_password/${DB_PASSWORD}/" .env
        
        log_info ".env 文件已创建，请根据需要修改配置"
    else
        log_info ".env 文件已存在"
    fi
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    docker-compose build --no-cache
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动数据库
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    docker-compose run --rm aksk-app ./migrate
    
    # 启动应用
    docker-compose up -d aksk-app
    
    # 启动 Nginx (可选)
    if [ "$1" = "--with-nginx" ]; then
        docker-compose up -d nginx
    fi
    
    log_info "服务启动完成"
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    docker-compose ps
    
    # 检查健康状态
    sleep 10
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "应用健康检查通过"
    else
        log_warn "应用健康检查失败，请检查日志"
    fi
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_info "服务已停止"
}

# 清理
cleanup() {
    log_info "清理资源..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    log_info "清理完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    docker-compose exec mysql mysqldump -u root -p"${DB_PASSWORD:-aksk123456}" aksk > "$BACKUP_DIR/database.sql"
    
    # 备份配置文件
    cp .env "$BACKUP_DIR/"
    
    log_info "数据备份完成: $BACKUP_DIR"
}

# 恢复数据
restore_data() {
    if [ -z "$1" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    BACKUP_DIR="$1"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    log_info "恢复数据从: $BACKUP_DIR"
    
    # 恢复数据库
    if [ -f "$BACKUP_DIR/database.sql" ]; then
        docker-compose exec -T mysql mysql -u root -p"${DB_PASSWORD:-aksk123456}" aksk < "$BACKUP_DIR/database.sql"
        log_info "数据库恢复完成"
    fi
    
    # 恢复配置文件
    if [ -f "$BACKUP_DIR/.env" ]; then
        cp "$BACKUP_DIR/.env" .
        log_info "配置文件恢复完成"
    fi
}

# 更新应用
update_app() {
    log_info "更新应用..."
    
    # 备份数据
    backup_data
    
    # 拉取最新代码
    git pull
    
    # 重新构建和部署
    build_images
    docker-compose up -d aksk-app
    
    log_info "应用更新完成"
}

# 主函数
main() {
    case "$1" in
        "deploy")
            check_dependencies
            create_directories
            generate_config
            build_images
            start_services "$2"
            check_status
            ;;
        "start")
            start_services "$2"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services "$2"
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_status
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "update")
            update_app
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "用法: $0 {deploy|start|stop|restart|logs|status|backup|restore|update|cleanup}"
            echo ""
            echo "命令说明:"
            echo "  deploy [--with-nginx]  - 完整部署应用"
            echo "  start [--with-nginx]   - 启动服务"
            echo "  stop                   - 停止服务"
            echo "  restart [--with-nginx] - 重启服务"
            echo "  logs                   - 查看日志"
            echo "  status                 - 检查状态"
            echo "  backup                 - 备份数据"
            echo "  restore <backup_dir>   - 恢复数据"
            echo "  update                 - 更新应用"
            echo "  cleanup                - 清理资源"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
