package main

import (
	"fmt"
	"log"
	"os"

	"aksk/internal/config"
	"aksk/internal/models"
	"aksk/internal/repository"
	"aksk/internal/utils"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	// 连接数据库
	db, err := repository.NewDatabase(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 创建 Token 仓库
	tokenRepo := repository.NewTokenRepository(db.GetDB())

	// 检查是否已存在管理员 Token
	tokens, _, err := tokenRepo.List(0, 1, nil)
	if err != nil {
		log.Fatalf("Failed to check existing tokens: %v", err)
	}

	if len(tokens) > 0 {
		fmt.Println("管理员 Token 已存在，无需重复创建")
		fmt.Println("如需重新创建，请先删除现有 Token")
		return
	}

	// 生成 Token 值
	tokenValue, err := utils.GenerateToken()
	if err != nil {
		log.Fatalf("Failed to generate token: %v", err)
	}

	// 计算 Token 哈希
	tokenHash, err := utils.HashPassword(tokenValue)
	if err != nil {
		log.Fatalf("Failed to hash token: %v", err)
	}

	// 创建管理员权限
	permissions := &models.Permission{
		Businesses: []string{"read", "write"},
		AccessKeys: []string{"read", "write"},
		Tokens:     []string{"read", "write"},
	}

	// 创建 Token 记录
	token := &models.Token{
		TokenName: "admin-token",
		TokenHash: tokenHash,
		Status:    1, // 启用
	}

	// 设置权限
	if err := token.SetPermissions(permissions); err != nil {
		log.Fatalf("Failed to set permissions: %v", err)
	}

	// 保存到数据库
	if err := tokenRepo.Create(token); err != nil {
		log.Fatalf("Failed to create admin token: %v", err)
	}

	// 输出结果
	fmt.Println("========================================")
	fmt.Println("管理员 Token 创建成功！")
	fmt.Println("========================================")
	fmt.Printf("Token Name: %s\n", token.TokenName)
	fmt.Printf("Token Value: %s\n", tokenValue)
	fmt.Println("权限: 完整的管理权限")
	fmt.Println("========================================")
	fmt.Println("请妥善保存此 Token，它将用于系统管理。")
	fmt.Println("示例使用方法:")
	fmt.Printf("curl -H \"Authorization: Bearer %s\" http://localhost:8080/api/v1/businesses\n", tokenValue)
	fmt.Println("========================================")

	// 可选：将 Token 保存到文件
	if len(os.Args) > 1 && os.Args[1] == "--save-to-file" {
		filename := "admin-token.txt"
		file, err := os.Create(filename)
		if err != nil {
			log.Printf("Warning: Failed to create token file: %v", err)
			return
		}
		defer file.Close()

		content := fmt.Sprintf("Admin Token: %s\nCreated: %s\n", tokenValue, token.CreatedAt.Format("2006-01-02 15:04:05"))
		if _, err := file.WriteString(content); err != nil {
			log.Printf("Warning: Failed to write token file: %v", err)
			return
		}

		fmt.Printf("Token 已保存到文件: %s\n", filename)
		fmt.Println("注意：请确保文件安全，建议使用后删除。")
	}
}
