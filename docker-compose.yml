version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: aksk-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-aksk123456}
      MYSQL_DATABASE: ${DB_DATABASE:-aksk}
      MYSQL_USER: ${DB_USERNAME:-aksk}
      MYSQL_PASSWORD: ${DB_PASSWORD:-aksk123456}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
    networks:
      - aksk-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis (可选，用于缓存和会话存储)
  redis:
    image: redis:7-alpine
    container_name: aksk-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-aksk123456}
    networks:
      - aksk-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # AK/SK 管理系统应用
  aksk-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aksk-app
    restart: unless-stopped
    ports:
      - "${SERVER_PORT:-8080}:8080"
    environment:
      # 服务器配置
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      
      # 数据库配置
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: ${DB_USERNAME:-aksk}
      DB_PASSWORD: ${DB_PASSWORD:-aksk123456}
      DB_DATABASE: ${DB_DATABASE:-aksk}
      
      # 安全配置
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your-32-byte-encryption-key-here!!}
      JWT_SECRET: ${JWT_SECRET:-your-jwt-secret-key-here}
      
      # 日志配置
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_FORMAT: json
      LOG_OUTPUT: stdout
    volumes:
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - aksk-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      timeout: 3s
      retries: 5
      start_period: 30s

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: aksk-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - aksk-app
    networks:
      - aksk-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  aksk-network:
    driver: bridge
