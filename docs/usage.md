# AK/SK 管理系统使用说明

## 概述

AK/SK 管理系统是一个企业级的访问密钥管理解决方案，提供安全、高效的 Access Key ID 和 Secret Access Key 管理功能。

## 快速开始

### 1. 环境准备

#### 系统要求
- Go 1.21+
- MySQL 8.0+
- Docker & Docker Compose（可选）

#### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd aksk

# 安装 Go 依赖
make deps
```

### 2. 配置系统

#### 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

#### 重要配置项
- `ENCRYPTION_KEY`: 32字节加密密钥，用于 AK/SK 加密存储
- `JWT_SECRET`: JWT 签名密钥
- `DB_PASSWORD`: 数据库密码

### 3. 部署方式

#### 方式一：Docker 部署（推荐）
```bash
# 一键部署
./scripts/deploy.sh deploy

# 或使用 Make
make docker-run
```

#### 方式二：本地部署
```bash
# 1. 启动 MySQL 数据库
# 2. 运行数据库迁移
make migrate

# 3. 启动应用
make run
```

### 4. 验证部署

```bash
# 检查健康状态
curl http://localhost:8080/health

# 预期响应
{
  "status": "healthy",
  "database": "connected"
}
```

## 核心功能

### 1. 业务管理

业务是 AK/SK 的逻辑分组单位，每个业务可以包含多个访问密钥。

#### 创建业务
```bash
curl -X POST http://localhost:8080/api/v1/businesses \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "web-service",
    "description": "Web服务业务"
  }'
```

#### 查看业务列表
```bash
curl -X GET http://localhost:8080/api/v1/businesses \
  -H "Authorization: Bearer <token>"
```

### 2. 访问密钥管理

#### 创建访问密钥
```bash
curl -X POST http://localhost:8080/api/v1/access-keys \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "business_id": 1,
    "description": "生产环境密钥",
    "expires_at": "2024-12-31T23:59:59Z"
  }'
```

**重要**: 创建时返回的 `secret_access_key` 只显示一次，请妥善保存。

#### 验证访问密钥
```bash
curl -X POST http://localhost:8080/api/v1/access-keys/validate \
  -H "Content-Type: application/json" \
  -d '{
    "access_key_id": "AK1234567890ABCDEF",
    "secret_access_key": "SK1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  }'
```

#### 根据业务获取密钥
```bash
curl -X GET http://localhost:8080/api/v1/businesses/web-service/access-keys \
  -H "Authorization: Bearer <token>"
```

### 3. Token 管理

系统使用 Token 进行 API 认证，支持细粒度权限控制。

#### 权限模型
- **资源类型**: `businesses`, `access_keys`, `tokens`
- **操作类型**: `read`, `write`

#### 权限配置示例
```json
{
  "businesses": ["read", "write"],
  "access_keys": ["read", "write"],
  "tokens": ["read"]
}
```

## 安全特性

### 1. 数据加密
- AK/SK 使用 AES-256-GCM 加密存储
- 支持密钥轮换
- 传输过程使用 HTTPS

### 2. 访问控制
- 基于 Token 的认证
- 细粒度权限控制
- IP 白名单支持

### 3. 安全防护
- API 限流保护
- SQL 注入防护
- XSS 攻击防护
- 安全头设置

### 4. 审计日志
- 完整的操作日志记录
- 包含用户、时间、操作类型等信息
- 支持日志查询和分析

## 运维管理

### 1. 监控检查

#### 健康检查
```bash
curl http://localhost:8080/health
```

#### 查看应用日志
```bash
# Docker 部署
docker-compose logs -f aksk-app

# 本地部署
tail -f logs/app.log
```

### 2. 数据备份

#### 备份数据
```bash
./scripts/deploy.sh backup
```

#### 恢复数据
```bash
./scripts/deploy.sh restore backups/20231201_120000
```

### 3. 应用更新

```bash
# 更新应用（包含自动备份）
./scripts/deploy.sh update

# 或手动更新
git pull
make build
docker-compose up -d aksk-app
```

### 4. 性能优化

#### 数据库优化
- 定期清理过期的访问密钥
- 清理旧的操作日志
- 优化数据库索引

#### 应用优化
- 调整连接池大小
- 配置适当的限流参数
- 启用 Gzip 压缩

## 故障排除

### 1. 常见问题

#### 数据库连接失败
```bash
# 检查数据库状态
docker-compose ps mysql

# 查看数据库日志
docker-compose logs mysql
```

#### 应用启动失败
```bash
# 检查配置文件
cat .env

# 查看应用日志
docker-compose logs aksk-app
```

#### Token 认证失败
- 检查 Token 是否正确
- 确认 Token 未过期
- 验证权限配置

### 2. 日志分析

#### 应用日志位置
- Docker: `docker-compose logs`
- 本地: `logs/app.log`

#### 关键日志字段
- `level`: 日志级别
- `msg`: 日志消息
- `error`: 错误信息
- `request_id`: 请求ID

### 3. 性能问题

#### 响应时间过长
- 检查数据库连接池
- 分析慢查询日志
- 优化 API 调用频率

#### 内存使用过高
- 检查连接泄漏
- 调整 GC 参数
- 监控 Goroutine 数量

## 最佳实践

### 1. 安全建议

- 定期轮换加密密钥
- 设置访问密钥过期时间
- 启用 HTTPS
- 配置防火墙规则
- 定期更新系统

### 2. 运维建议

- 设置监控告警
- 定期备份数据
- 监控系统资源
- 建立应急响应流程

### 3. 开发建议

- 使用环境变量管理配置
- 实现优雅关闭
- 添加健康检查
- 编写单元测试

## 开发指南

### 1. 本地开发

```bash
# 设置开发环境
make dev-setup

# 启动开发模式（热重载）
make dev

# 运行测试
make test

# 代码质量检查
make quality
```

### 2. 测试

```bash
# 运行所有测试
make test

# 运行覆盖率测试
make test-coverage

# 运行基准测试
make bench
```

### 3. 构建部署

```bash
# 构建应用
make build

# 构建所有平台
make build-all

# 构建 Docker 镜像
make docker-build
```

## 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查应用日志
3. 查看 GitHub Issues
4. 联系技术支持团队

## 版本更新

系统支持平滑升级，更新步骤：

1. 备份当前数据
2. 拉取最新代码
3. 运行数据库迁移
4. 重启应用服务
5. 验证功能正常
