# AK/SK 管理系统

基于 Golang 和 MySQL 的企业级 Access Key ID 和 Secret Access Key 管理系统。

## 功能特性

- 🔐 安全的 AK/SK 管理（增删改查）
- 🏢 多业务支持
- 🔑 Token 认证机制
- 🛡️ 企业级安全保障
- 📊 完整的日志记录
- 🚀 高性能 API 接口
- 🐳 Docker 容器化部署

## 系统架构

```
aksk/
├── cmd/                    # 应用程序入口
│   └── server/
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── models/            # 数据模型
│   ├── handlers/          # HTTP 处理器
│   ├── middleware/        # 中间件
│   ├── services/          # 业务逻辑
│   ├── repository/        # 数据访问层
│   └── utils/             # 工具函数
├── migrations/            # 数据库迁移
├── docs/                  # 文档
├── docker/                # Docker 配置
└── tests/                 # 测试文件
```

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Docker & Docker Compose (推荐)

### 方式一：Docker 部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd aksk

# 2. 一键部署
./scripts/deploy.sh deploy

# 3. 创建管理员 Token
go run scripts/init-admin-token.go

# 4. 验证部署
curl http://localhost:8080/health
```

### 方式二：本地开发部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd aksk

# 2. 安装依赖
make deps

# 3. 设置开发环境
make dev-setup

# 4. 配置数据库连接
# 编辑 .env 文件，设置数据库连接信息

# 5. 运行数据库迁移
make migrate

# 6. 启动服务
make run

# 7. 创建管理员 Token
go run scripts/init-admin-token.go
```

### 验证安装

```bash
# 健康检查
curl http://localhost:8080/health

# 使用管理员 Token 测试 API
curl -H "Authorization: Bearer <your-admin-token>" \
     http://localhost:8080/api/v1/businesses
```

## 使用指南

### 常用命令

```bash
# 开发相关
make dev-setup    # 设置开发环境
make dev          # 开发模式运行（热重载）
make test         # 运行测试
make quality      # 代码质量检查

# 构建部署
make build        # 构建应用
make docker-build # 构建 Docker 镜像
make docker-run   # 运行 Docker 容器

# 运维管理
./scripts/deploy.sh deploy    # 完整部署
./scripts/deploy.sh backup    # 备份数据
./scripts/deploy.sh logs      # 查看日志
./scripts/deploy.sh status    # 检查状态
```

### 创建第一个业务和密钥

```bash
# 1. 创建业务
curl -X POST http://localhost:8080/api/v1/businesses \
  -H "Authorization: Bearer <admin-token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "my-service", "description": "我的服务"}'

# 2. 创建访问密钥
curl -X POST http://localhost:8080/api/v1/access-keys \
  -H "Authorization: Bearer <admin-token>" \
  -H "Content-Type: application/json" \
  -d '{"business_id": 1, "description": "生产环境密钥"}'

# 3. 验证访问密钥
curl -X POST http://localhost:8080/api/v1/access-keys/validate \
  -H "Content-Type: application/json" \
  -d '{"access_key_id": "AK...", "secret_access_key": "SK..."}'
```

## 文档

- [API 文档](docs/api.md) - 完整的 API 接口文档
- [使用说明](docs/usage.md) - 详细的使用指南
- [部署指南](docs/deployment.md) - 生产环境部署建议

## 安全性

- AK/SK 采用 AES-256 加密存储
- JWT Token 认证
- API 请求限流
- 完整的操作日志
- 输入参数验证

## 许可证

MIT License
